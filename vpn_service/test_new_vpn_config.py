#!/usr/bin/env python3
"""
Тест нового эндпоинта GET /api/vpn/config/ с шаблонным подходом.

Этот скрипт тестирует новую реализацию эндпоинта, которая:
1. Извлекает hiddify_uuid из JWT токена
2. Использует шаблонный подход на основе singbox_Config_example
3. Подставляет UUID пользователя в конфигурацию
4. Возвращает чистую JSON конфигурацию без обертки
"""

import os
import sys
import django
import json
import uuid
from datetime import timedelta

# Настройка Django
sys.path.append('/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

# Добавляем testserver в ALLOWED_HOSTS для тестирования
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from accounts.models import UserAccount, UserDevice, HiddifyLink
from subscriptions.models import SubscriptionPlan, ActiveSubscription


def create_test_user_with_subscription():
    """Создает тестового пользователя с активной подпиской и hiddify_uuid."""
    print("🔧 Создание тестового пользователя...")
    
    # Создаем пользователя
    user, created = UserAccount.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'username': '<EMAIL>',
            'is_anonymous': False,
            'is_active': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    print(f"✅ Пользователь: {user.email} ({'создан' if created else 'найден'})")
    
    # Создаем устройство
    device, created = UserDevice.objects.get_or_create(
        user=user,
        device_id="test-vpn-device-123",
        defaults={
            'device_name': 'Test VPN Device',
            'device_type': 'test',
            'is_active': True
        }
    )
    print(f"✅ Устройство: {device.device_id} ({'создано' if created else 'найдено'})")
    
    # Создаем тарифный план
    plan, created = SubscriptionPlan.objects.get_or_create(
        name="Test VPN Plan",
        defaults={
            'price': 9.99,
            'duration_days': 30,
            'traffic_limit_gb': 100,
            'max_devices': 5,
            'is_active': True
        }
    )
    print(f"✅ План: {plan.name} ({'создан' if created else 'найден'})")
    
    # Создаем активную подписку
    subscription, created = ActiveSubscription.objects.get_or_create(
        user=user,
        is_active=True,
        defaults={
            'plan': plan,
            'start_date': timezone.now(),
            'end_date': timezone.now() + timedelta(days=plan.duration_days)
        }
    )
    print(f"✅ Подписка: {subscription.plan.name} ({'создана' if created else 'найдена'})")
    
    # Создаем HiddifyLink с тестовым UUID
    test_hiddify_uuid = str(uuid.uuid4())
    hiddify_link, created = HiddifyLink.objects.get_or_create(
        user=user,
        defaults={
            'device': device,
            'hiddify_user_uuid': test_hiddify_uuid,
            'hiddify_comment': {'test': True},
            'traffic_limit_bytes': plan.traffic_limit_gb * 1024 * 1024 * 1024,
            'is_active_in_hiddify': True,
            'hiddify_created_at': timezone.now()
        }
    )
    print(f"✅ HiddifyLink: {hiddify_link.hiddify_user_uuid} ({'создан' if created else 'найден'})")
    
    return user, device, subscription, hiddify_link


def create_jwt_token_with_hiddify_uuid(user, device, hiddify_uuid):
    """Создает JWT токен с hiddify_uuid в payload."""
    print("🔑 Создание JWT токена с hiddify_uuid...")
    
    refresh = RefreshToken.for_user(user)
    access_token = refresh.access_token
    
    # Добавляем дополнительные claims
    access_token['userId'] = str(user.id)
    access_token['deviceId'] = str(device.id)
    access_token['tokenType'] = 'registered'
    access_token['device_id'] = device.device_id
    access_token['hiddify_uuid'] = str(hiddify_uuid)
    
    print(f"✅ JWT токен создан с hiddify_uuid: {hiddify_uuid}")
    return str(access_token)


def test_vpn_config_endpoint():
    """Тестирует новый эндпоинт GET /api/vpn/config/."""
    print("\n🧪 Тестирование эндпоинта GET /api/vpn/config/")
    print("=" * 60)
    
    # Создаем тестовые данные
    user, device, subscription, hiddify_link = create_test_user_with_subscription()
    
    # Создаем JWT токен с hiddify_uuid
    jwt_token = create_jwt_token_with_hiddify_uuid(user, device, hiddify_link.hiddify_user_uuid)
    
    # Создаем API клиент
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {jwt_token}')
    
    print("\n📡 Отправка запроса к эндпоинту...")
    response = client.get('/api/vpn/config/')
    
    print(f"📊 Статус ответа: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Запрос выполнен успешно!")
        
        # Проверяем структуру ответа
        config_data = response.json()
        
        print("\n🔍 Анализ структуры конфигурации:")
        print(f"  - Есть секция 'dns': {'dns' in config_data}")
        print(f"  - Есть секция 'inbounds': {'inbounds' in config_data}")
        print(f"  - Есть секция 'outbounds': {'outbounds' in config_data}")
        print(f"  - Есть секция 'route': {'route' in config_data}")
        
        # Проверяем персонализацию
        if 'outbounds' in config_data:
            hiddify_uuid_str = str(hiddify_link.hiddify_user_uuid)
            personalized_count = 0
            
            for outbound in config_data['outbounds']:
                if outbound.get('type') == 'trojan' and outbound.get('password') == hiddify_uuid_str:
                    personalized_count += 1
                    print(f"  ✅ Trojan outbound персонализирован: {outbound['tag']}")
                elif outbound.get('type') == 'vmess' and outbound.get('uuid') == hiddify_uuid_str:
                    personalized_count += 1
                    print(f"  ✅ VMess outbound персонализирован: {outbound['tag']}")
            
            print(f"\n🎯 Персонализированных outbound'ов: {personalized_count}")
            
            if personalized_count > 0:
                print("✅ Персонализация работает корректно!")
            else:
                print("❌ Персонализация не работает!")
        
        # Проверяем соответствие структуре singbox_Config_example
        expected_keys = ['dns', 'inbounds', 'outbounds', 'route']
        missing_keys = [key for key in expected_keys if key not in config_data]
        
        if not missing_keys:
            print("✅ Структура соответствует singbox_Config_example")
        else:
            print(f"❌ Отсутствуют ключи: {missing_keys}")
        
        # Сохраняем конфигурацию в файл для проверки
        with open('/tmp/test_vpn_config.json', 'w') as f:
            json.dump(config_data, f, indent=2)
        print(f"💾 Конфигурация сохранена в /tmp/test_vpn_config.json")
        
        return True
        
    else:
        print(f"❌ Ошибка запроса: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   Детали ошибки: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
        except:
            print(f"   Текст ошибки: {response.content.decode()}")
        return False


def test_without_hiddify_uuid():
    """Тестирует эндпоинт без hiddify_uuid в токене."""
    print("\n🧪 Тестирование без hiddify_uuid в токене")
    print("=" * 50)
    
    user, device, subscription, hiddify_link = create_test_user_with_subscription()
    
    # Создаем JWT токен БЕЗ hiddify_uuid
    refresh = RefreshToken.for_user(user)
    access_token = refresh.access_token
    access_token['userId'] = str(user.id)
    access_token['deviceId'] = str(device.id)
    # НЕ добавляем hiddify_uuid
    
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {str(access_token)}')
    
    response = client.get('/api/vpn/config/')
    
    print(f"📊 Статус ответа: {response.status_code}")
    
    if response.status_code == 403:
        print("✅ Корректно возвращает 403 без hiddify_uuid")
        return True
    else:
        print(f"❌ Ожидался статус 403, получен {response.status_code}")
        return False


def main():
    """Основная функция тестирования."""
    print("🚀 Тестирование нового эндпоинта GET /api/vpn/config/")
    print("=" * 70)
    
    success_count = 0
    total_tests = 2
    
    # Тест 1: Нормальный запрос с hiddify_uuid
    if test_vpn_config_endpoint():
        success_count += 1
    
    # Тест 2: Запрос без hiddify_uuid
    if test_without_hiddify_uuid():
        success_count += 1
    
    print(f"\n📈 Результаты тестирования:")
    print(f"   Успешных тестов: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 Все тесты прошли успешно!")
        return True
    else:
        print("❌ Некоторые тесты не прошли")
        return False


if __name__ == '__main__':
    main()
