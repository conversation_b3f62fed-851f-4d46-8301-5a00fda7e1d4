#!/usr/bin/env python3
"""
HTTP тест нового эндпоинта GET /api/vpn/config/ через реальные HTTP запросы.

Этот скрипт:
1. Создает тестового пользователя через API
2. Получает JWT токен с hiddify_uuid
3. Тестирует новый эндпоинт GET /api/vpn/config/
4. Проверяет персонализацию конфигурации
"""

import requests
import json
import os
import sys
import django
from datetime import timedelta

# Настройка Django
sys.path.append('/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

from django.utils import timezone
from accounts.models import UserAccount, UserDevice, HiddifyLink
from subscriptions.models import SubscriptionPlan, ActiveSubscription

API_BASE = "http://ductuspro.ru:8090/api"


def create_test_user_in_db():
    """Создает тестового пользователя в базе данных."""
    print("🔧 Создание тестового пользователя в БД...")
    
    # Создаем пользователя
    user, created = UserAccount.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'username': '<EMAIL>',
            'is_anonymous': False,
            'is_active': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    print(f"✅ Пользователь: {user.email} ({'создан' if created else 'найден'})")
    
    # Создаем устройство
    device, created = UserDevice.objects.get_or_create(
        user=user,
        device_id="http-test-device-123",
        defaults={
            'device_name': 'HTTP Test Device',
            'device_type': 'test',
            'is_active': True
        }
    )
    print(f"✅ Устройство: {device.device_id} ({'создано' if created else 'найдено'})")
    
    # Создаем тарифный план
    plan, created = SubscriptionPlan.objects.get_or_create(
        name="HTTP Test Plan",
        defaults={
            'price': 9.99,
            'duration_days': 30,
            'traffic_limit_gb': 100,
            'max_devices': 5,
            'is_active': True
        }
    )
    print(f"✅ План: {plan.name} ({'создан' if created else 'найден'})")
    
    # Создаем активную подписку
    subscription, created = ActiveSubscription.objects.get_or_create(
        user=user,
        is_active=True,
        defaults={
            'plan': plan,
            'start_date': timezone.now(),
            'end_date': timezone.now() + timedelta(days=plan.duration_days)
        }
    )
    print(f"✅ Подписка: {subscription.plan.name} ({'создана' if created else 'найдена'})")
    
    # Создаем HiddifyLink с тестовым UUID
    import uuid
    test_hiddify_uuid = str(uuid.uuid4())
    hiddify_link, created = HiddifyLink.objects.get_or_create(
        user=user,
        defaults={
            'device': device,
            'hiddify_user_uuid': test_hiddify_uuid,
            'hiddify_comment': {'test': True, 'http_test': True},
            'traffic_limit_bytes': plan.traffic_limit_gb * 1024 * 1024 * 1024,
            'is_active_in_hiddify': True,
            'hiddify_created_at': timezone.now()
        }
    )
    print(f"✅ HiddifyLink: {hiddify_link.hiddify_user_uuid} ({'создан' if created else 'найден'})")
    
    return user, device, subscription, hiddify_link


def get_jwt_token(email, password, device_id):
    """Получает JWT токен через API логина."""
    print(f"🔑 Получение JWT токена для {email}...")
    
    response = requests.post(f"{API_BASE}/auth/login/", json={
        'email': email,
        'password': password,
        'device_id': device_id
    })
    
    if response.status_code == 200:
        data = response.json()
        token = data['tokens']['access']
        print("✅ JWT токен получен успешно")
        return token
    else:
        print(f"❌ Ошибка получения токена: {response.status_code}")
        print(response.text)
        return None


def test_vpn_config_endpoint(token):
    """Тестирует эндпоинт GET /api/vpn/config/ через HTTP."""
    print("\n🧪 HTTP тестирование эндпоинта GET /api/vpn/config/")
    print("=" * 60)
    
    headers = {'Authorization': f'Bearer {token}'}
    
    print("📡 Отправка HTTP запроса...")
    response = requests.get(f"{API_BASE}/vpn/config/", headers=headers)
    
    print(f"📊 HTTP статус: {response.status_code}")
    print(f"📋 Content-Type: {response.headers.get('Content-Type', 'не указан')}")
    print(f"📋 Cache-Control: {response.headers.get('Cache-Control', 'не указан')}")
    
    if response.status_code == 200:
        print("✅ HTTP запрос выполнен успешно!")
        
        try:
            config_data = response.json()
            
            print("\n🔍 Анализ HTTP ответа:")
            print(f"  - Размер ответа: {len(response.content)} байт")
            print(f"  - Есть секция 'dns': {'dns' in config_data}")
            print(f"  - Есть секция 'inbounds': {'inbounds' in config_data}")
            print(f"  - Есть секция 'outbounds': {'outbounds' in config_data}")
            print(f"  - Есть секция 'route': {'route' in config_data}")
            
            # Проверяем, что это чистая конфигурация без обертки
            wrapper_fields = ['success', 'config_type', 'config', 'location_info', 'subscription_info']
            has_wrapper = any(field in config_data for field in wrapper_fields)
            
            if has_wrapper:
                print("❌ Ответ содержит обертку (не чистая конфигурация)")
            else:
                print("✅ Ответ содержит чистую SingBox конфигурацию")
            
            # Проверяем персонализацию
            if 'outbounds' in config_data:
                personalized_count = 0
                
                for outbound in config_data['outbounds']:
                    if outbound.get('type') == 'trojan' and 'password' in outbound:
                        personalized_count += 1
                        print(f"  ✅ Trojan outbound персонализирован: {outbound['tag']}")
                    elif outbound.get('type') == 'vmess' and 'uuid' in outbound:
                        personalized_count += 1
                        print(f"  ✅ VMess outbound персонализирован: {outbound['tag']}")
                
                print(f"\n🎯 Персонализированных outbound'ов: {personalized_count}")
                
                if personalized_count > 0:
                    print("✅ Персонализация работает корректно!")
                    return True
                else:
                    print("❌ Персонализация не работает!")
                    return False
            else:
                print("❌ Отсутствует секция outbounds")
                return False
                
        except json.JSONDecodeError:
            print("❌ Ответ не является валидным JSON")
            print(f"Первые 500 символов ответа: {response.text[:500]}")
            return False
            
    else:
        print(f"❌ HTTP ошибка: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   Детали ошибки: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
        except:
            print(f"   Текст ошибки: {response.text}")
        return False


def test_without_auth():
    """Тестирует эндпоинт без аутентификации."""
    print("\n🧪 HTTP тестирование без аутентификации")
    print("=" * 50)
    
    response = requests.get(f"{API_BASE}/vpn/config/")
    
    print(f"📊 HTTP статус: {response.status_code}")
    
    if response.status_code == 401:
        print("✅ Корректно возвращает 401 без аутентификации")
        return True
    else:
        print(f"❌ Ожидался статус 401, получен {response.status_code}")
        return False


def main():
    """Основная функция HTTP тестирования."""
    print("🚀 HTTP тестирование нового эндпоинта GET /api/vpn/config/")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    # Создаем тестовые данные в БД
    user, device, subscription, hiddify_link = create_test_user_in_db()
    
    # Тест 1: Получение JWT токена
    jwt_token = get_jwt_token(user.email, 'testpass123', device.device_id)
    if jwt_token:
        success_count += 1
        print("✅ Тест 1: JWT токен получен")
    else:
        print("❌ Тест 1: Не удалось получить JWT токен")
    
    # Тест 2: Нормальный запрос с JWT токеном
    if jwt_token and test_vpn_config_endpoint(jwt_token):
        success_count += 1
        print("✅ Тест 2: Эндпоинт работает с JWT токеном")
    else:
        print("❌ Тест 2: Эндпоинт не работает с JWT токеном")
    
    # Тест 3: Запрос без аутентификации
    if test_without_auth():
        success_count += 1
        print("✅ Тест 3: Корректная обработка запросов без аутентификации")
    else:
        print("❌ Тест 3: Некорректная обработка запросов без аутентификации")
    
    print(f"\n📈 Результаты HTTP тестирования:")
    print(f"   Успешных тестов: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 Все HTTP тесты прошли успешно!")
        return True
    else:
        print("❌ Некоторые HTTP тесты не прошли")
        return False


if __name__ == '__main__':
    main()
